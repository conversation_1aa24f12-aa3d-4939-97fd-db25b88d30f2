//
//  ProductTrackingInfoController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/3.
//

class ProductTrackingInfoController: BasePresentController {
 
    /// 选择结果
    let selectCompletePublisher = PassthroughSubject<Bool, Never>()
    private lazy var deliveryContentView = DeliveryContentView().then{
        $0.arrowButton.isHidden = true
    }
    lazy var orderLogistics = OrderLogistics()
    ///快递物流信息父视图
    lazy var scorllView = UIScrollView()
    // MARK: - 生命周期
  
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }

    
    override func configUI() {
        configView(title: "快递进度", bottomTitle: "确定")
        self.hideBottomBar()
        contentView.addSubview(deliveryContentView)
        deliveryContentView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
        }
        deliveryContentView.configTitle(express_company: orderLogistics.express_company, express_no: orderLogistics.express_no)
        contentView.addSubview(scorllView)
        scorllView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(12)
            make.top.equalTo(deliveryContentView.snp.bottom).offset(12)
            make.bottom.equalTo(-ScreenInfo.totalTabBarHeight)
            make.height.equalTo(400)
        }
        

       
    }
}

