//
//  ProductOrderModel.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/20.
//

import Foundation
import SmartCodable
import UIKit
/// 订单事件操作类型
enum OrderEventAction: String {
    // MARK: - 待付款状态 (状态1)
    /// 去付款 (买家)
    case pay = "pay"
    /// 取消订单 (买家)
    case cancel = "cancel"
    /// 取消订单 (卖家)
    case cancelOrder = "cancel_order"
    
    // MARK: - 待发货状态 (状态2)
    /// 查看快递 (买家)
    case viewLogistics = "view_logistics"
    /// 去发货 (卖家)
    case ship = "ship"
    
    // MARK: - 已发货状态 (状态3)
    /// 确认收货 (买家)
    case confirm = "confirm"
    
    // MARK: - 已收货状态 (状态4)
    /// 去评价 (买家)
    case review = "review"
    /// 查看评价 (买家/卖家)
    case viewReview = "view_review"
    
    // MARK: - 退款处理状态 (状态5-7)
    /// 取消退款申请 (买家/卖家)
    case cancelRefund = "cancel_refund"
    /// 同意退款 (卖家)
    case agreeRefund = "agree_refund"
    /// 拒绝退款 (卖家)
    case rejectRefund = "reject_refund"
    /// 去发货(退货) (买家)
    case returnGoods = "return_goods"
    /// 确认收货并退款 (卖家)
    case confirmReceiveAndRefund = "confirm_receive_and_refund"
    /// 拒绝退货 (卖家)
    case rejectReturnGoods = "reject_return_goods"
}

/// 订单操作按钮模型
struct ActionButton: SmartCodable {
    /// 按钮文字
    var title: String = ""
    var text: String = ""
    /// 按钮操作类型
    var action: String = ""
    /// 按钮文字颜色
    var title_color: String = ""
    /// 按钮背景颜色
    var bg_color: String = ""
}

/// 订单项模型（根据新接口调整）
struct OrderItem: SmartCodable {
    /// 订单项ID
    var id: Int = 0
    /// 明细类型（1商品、2服务）
    var item_type: Int = 1
    /// 数量
    var quantity: Int = 1
    /// 单价
    var price: Float = 0.0
    /// 总价
    var total_price: Float = 0.0
    /// 是否已退款（0否，1是）
    var is_refunded: Int = 0
    /// 退款时间
    var refunded_at: Int?
    /// 商品或服务标题
    var title: String = ""
    /// 商品规格文本
    var spec_value_text: String = ""
    /// 缩略图URL
    var thumb_image: String = ""
    /// 服务帖位置（仅服务帖有）
    var location: String = ""
    /// 原价
    var origin_price: String = ""
    /// 运费类型
    var postage_type: Int = 0
    /// 运费金额
    var postage_fee: Float = 0.0
    /// 运费说明文本
    var postage_type_text: String = ""

    /// 获取显示的图片URL
    var displayImageUrl: String {
        return thumb_image
    }

    /// 获取显示的标题
    var displayTitle: String {
        return title
    }
}



/// 订单模型（根据新接口调整）
struct ProductOrderModel: SmartCodable {
    /// 订单ID
    var order_id: Int = 0
    /// 订单编号
    var order_sn: String = ""
    /// 订单类型（1商品订单，2服务订单）
    var order_type: Int = 1
    /// 订单总金额
    var total_amount: Float = 0.0
    /// 卖家视角订单状态
    var seller_status: Int = 0
    /// 卖家订单状态文字
    var seller_status_text: String = ""
    /// 买家视角状态码
    var buyer_status: Int = 0
    /// 买家状态中文
    var buyer_status_text: String = ""
    /// 卖家昵称
    var seller_name: String = ""
    /// 卖家头像
    var seller_avatar: String = ""
    /// 支付宝交易号
    var alipay_trade_no: String?
    /// 微信交易号
    var wechat_trade_no: String?
    /// 创建时间
    var created_at: String = ""
    /// 可操作按钮列表
    var action_buttons: [ActionButton] = []
    /// 订单项列表
    var items: [OrderItem] = []

    /// 获取第一个订单项（用于显示）
    var firstItem: OrderItem? {
        return items.first
    }

    /// 获取订单类型文字
    var orderTypeText: String {
        return order_type == 1 ? "商品订单" : "服务订单"
    }
}

// MARK: - 订单创建响应模型

/// 创建订单响应模型
struct CreateOrderResponse: SmartCodable {
    /// 成功创建的订单ID数组（购物车创建订单可能返回多个）
    var order_ids: [Int] = []
    /// 单个订单ID（服务订单和立即购买返回单个）
    var order_id: Int = 0
}

struct OrderDetailResponse: SmartCodable {
    var order: OrderDetailModel = OrderDetailModel()
    var status_map: [OrderDetailStatus] = [OrderDetailStatus]()
}
struct OrderDetailStatus: SmartCodable, Equatable {
    ///进度状态码
    var sort: Int = 0
    ///进度状态对应文字
    var value: String = ""

    /// 实现Equatable协议
    static func == (lhs: OrderDetailStatus, rhs: OrderDetailStatus) -> Bool {
        return lhs.sort == rhs.sort && lhs.value == rhs.value
    }
}
/// 订单详情模型
struct OrderDetailModel: SmartCodable {
    /// 订单ID
    var order_id: Int = 0
    /// 订单编号
    var order_sn: String = ""
    /// 订单类型：1商品，2服务
    var order_type: Int = 1
    /// 实际支付金额（成交价）
    var paid_amount: Float = 0.0
    /// 商品总金额
    var final_amount: Float = 0.0
    /// 运费
    var postage_fee: Float = 0.0
    /// 使用的优惠金额
    var discount_amount: Float = 0.0
    /// 当前状态码
    var status: Int = 0
    ///截止时间2025-07-26 11:11:11，有值则显示详细文字
    var status_deadline: String = ""
    /// 当前状态文字
    var status_subtitle: String = ""
    /// 当前状态详细文字拼接在倒计时后面
    var status_detail_text: String = ""
    /// 状态文字描述
    var status_text: String = ""
    /// 下单时间
    var created_at: String = ""
    /// 支付时间
    var paid_at: String? = nil
    /// 发货时间
    var shipped_at: String? = nil
    /// 支付宝交易号
    var alipay_trade_no: String? = nil
    /// 微信支付单号
    var wechat_trade_no: String? = nil
    /// 卖家昵称
    var seller_name: String = ""
    /// 卖家头像
    var seller_avatar: String = ""
    /// 总价（单价 * 数量）
    var total_price: Float = 0.0
    /// 卖家用户ID
    var seller_id: Int = 0
    ///是否可以退款
    var is_refunded_button:Bool = false
    /// 可操作按钮列表
    var action_buttons: [ActionButton] = []
    /// 订单项数组（商品/服务）
    var items: [OrderDetailItem] = []
    /// 收货地址信息
    var address: OrderAddress = OrderAddress()
    /// 物流信息
    var logistics: OrderLogistics = OrderLogistics()
    
    var status_map: [OrderDetailStatus] = [OrderDetailStatus]()

}

/// 订单详情项模型
struct OrderDetailItem: SmartCodable {
    /// 订单项ID
    var id: Int = 0
    var product_id: Int = 0
    /// 类型：1商品，2服务帖
    var item_type: Int = 1
    /// 商品/服务标题
    var title: String = ""
    /// 商品/服务封面图
    var thumb_image: String = ""
    /// 地址（服务帖）
    var location: String = ""
    /// 原始价格
    var origin_price: Float = 0.0
    /// 成交单价
    var price: Float = 0.0
    /// 总价（单价 * 数量）
    var total_price: Float = 0.0
    /// 数量
    var quantity: Int = 1
    /// 是否已退款（0未退款，1已退款）
    var is_refunded: Int = 0
    /// 退款时间
    var refunded_at: String? = nil
    /// 商品规格文字
    var spec_value_text: String = ""
    /// 运费类型（0免邮、1包邮、2自定义）
    var postage_type: Int = 0
    /// 运费金额
    var postage_fee: Float = 0.0
    /// 运费文字描述
    var postage_type_text: String = ""
    var isSelect = false
}

/// 订单地址模型
struct OrderAddress: SmartCodable {
    /// 收货人姓名
    var name: String = ""
    /// 联系电话
    var phone: String = ""
    /// 省份
    var province: String = ""
    /// 城市
    var city: String = ""
    /// 区县
    var district: String = ""
    /// 街道
    var street: String = ""
    /// 详细地址
    var detail: String = ""
    /// 全地址（拼接）
    var full_address: String = ""
}

/// 订单物流模型
struct OrderLogistics: SmartCodable {
    /// 快递单号
    var express_no: String = ""
    /// 快递公司名称
    var express_company: String = ""
    /// 快递公司编码
    var express_company_num: String = ""
    /// 快递状态描述
    var express_status: String = ""
}

// MARK: - 扩展方法
extension OrderDetailModel {

    /// 是否显示物流信息
    var shouldShowLogistics: Bool {
        return status >= 3 && !logistics.express_no.isEmpty
    }

    /// 是否显示倒计时
    var shouldShowCountdown: Bool {
        return status == 1 // 已付款状态显示倒计时
    }

    /// 格式化的支付金额
    var formattedPaidAmount: String {
        return String(format: "¥%.2f", paid_amount)
    }

    /// 格式化的商品总价
    var formattedFinalAmount: String {
        return String(format: "¥%.2f", final_amount)
    }

    /// 格式化的运费
    var formattedPostageFee: String {
        return postage_fee > 0 ? String(format: "¥%.2f", postage_fee) : "¥0"
    }

    /// 格式化的优惠金额
    var formattedDiscountAmount: String {
        return discount_amount > 0 ? String(format: "-¥%.2f", discount_amount) : "¥0"
    }
}

extension OrderDetailItem {

    /// 格式化的原价
    var formattedOriginPrice: String {
        return String(format: "¥%.2f", origin_price)
    }

    /// 格式化的成交价
    var formattedPrice: String {
        return String(format: "¥%.2f", price)
    }

    /// 格式化的总价
    var formattedTotalPrice: String {
        return String(format: "¥%.2f", total_price)
    }

    /// 是否显示退款标识
    var isRefunded: Bool {
        return is_refunded == 1
    }
}

// MARK: - 支付相关模型

/// 支付流程状态枚举
enum PaymentFlowState {
    case idle                           // 空闲状态
    case creatingOrder                  // 创建订单中
    case orderCreated(orderId: Int)     // 订单创建成功
    case initiatingPayment              // 发起支付中
    case waitingThirdPartyPayment       // 等待第三方支付
    case notifyingPaymentSuccess        // 通知支付成功中
    case paymentCompleted(orderId: Int) // 支付完成
    case paymentFailed(error: String)   // 支付失败
}

/// 支付请求参数
struct PaymentRequest: RequestParametersConvertible {
    /// 订单ID
    let order_id: Int
    /// 支付方式：wallet / alipay / wechat / mix
    let pay_method: String

    func asParameters() -> [String: Any] {
        return [
            "order_id": order_id,
            "pay_method": pay_method
        ]
    }
}

/// 支付响应模型
struct PaymentResponse: SmartCodable {
    /// 订单编号
    var order_sn: String = ""
    /// 支付金额
    var amount: Float = 0.0
    /// 支付宝签名字符串（支付宝支付时返回）
    var alipay_signed_string: String?
    /// 微信支付参数（微信支付时返回）
    var wechat_pay_params: String?
}

/// 支付成功通知请求参数
struct PaymentNotifyRequest: RequestParametersConvertible {
    /// 订单ID
    let order_id: Int
    /// 支付方式
    let pay_method: String
    /// 第三方支付交易号（可选）
    let trade_no: String?

    func asParameters() -> [String: Any] {
        var params: [String: Any] = [
            "order_id": order_id,
            "pay_method": pay_method
        ]

        if let trade_no = trade_no {
            params["trade_no"] = trade_no
        }

        return params
    }
}


